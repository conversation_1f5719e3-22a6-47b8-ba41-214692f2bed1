const { createCanvas, loadImage } = require('canvas');
const twemoji = require('@twemoji/api');
const fs = require('fs');

async function testTwemojiCanvas() {
    console.log('Starting Twemoji + Canvas test...');

    // Create a white canvas
    const canvas = createCanvas(800, 600);
    const ctx = canvas.getContext('2d');

    // Fill with white background
    ctx.fillStyle = 'white';
    ctx.fillRect(0, 0, canvas.width, canvas.height);

    // Test emojis to render
    const testEmojis = ['😀', '🎉', '❤️', '🚀', '🌟', '🔥'];

    console.log('Processing emojis using PNG format...');

    let x = 50;
    let y = 100;
    const emojiSize = 72;
    const spacing = 100;

    for (let i = 0; i < testEmojis.length; i++) {
        const emoji = testEmojis[i];
        console.log(`Processing emoji: ${emoji}`);

        try {
            // Use Twemoji API to parse the emoji and get PNG URL instead of SVG
            const twemojiHtml = twemoji.parse(emoji, {
                folder: '72x72',
                ext: '.png'
            });

            console.log(`Twemoji HTML for ${emoji}:`, twemojiHtml);

            // Extract PNG URL from the HTML using regex
            const pngUrlMatch = twemojiHtml.match(/src="([^"]+)"/);
            if (pngUrlMatch) {
                const pngUrl = pngUrlMatch[1];
                console.log(`PNG URL: ${pngUrl}`);

                // Fetch the PNG content
                const response = await fetch(pngUrl);
                if (response.ok) {
                    const pngBuffer = await response.arrayBuffer();

                    // Load the PNG as an image
                    const image = await loadImage(Buffer.from(pngBuffer));

                    // Draw the emoji on canvas
                    ctx.drawImage(image, x, y, emojiSize, emojiSize);

                    // Add text label below emoji
                    ctx.fillStyle = 'black';
                    ctx.font = '16px Arial';
                    ctx.textAlign = 'center';
                    ctx.fillText(emoji, x + emojiSize/2, y + emojiSize + 20);

                    console.log(`Successfully rendered emoji: ${emoji}`);
                } else {
                    console.error(`Failed to fetch PNG for ${emoji}: ${response.status}`);
                }
            } else {
                console.error(`Could not extract PNG URL from Twemoji HTML for ${emoji}`);
            }
        } catch (error) {
            console.error(`Error processing emoji ${emoji}:`, error);
        }

        // Move to next position
        x += spacing;
        if (x > canvas.width - emojiSize) {
            x = 50;
            y += spacing + 40;
        }
    }
    
    // Add title
    ctx.fillStyle = 'black';
    ctx.font = 'bold 24px Arial';
    ctx.textAlign = 'center';
    ctx.fillText('Twemoji + Canvas Test', canvas.width / 2, 40);
    
    // Save the canvas as PNG
    const buffer = canvas.toBuffer('image/png');
    const outputPath = './output/emotest.png';
    
    // Ensure output directory exists
    if (!fs.existsSync('./output')) {
        fs.mkdirSync('./output');
    }
    
    fs.writeFileSync(outputPath, buffer);
    console.log(`Test image saved to: ${outputPath}`);
    
    // Also test alternative Twemoji options
    console.log('\nTesting different Twemoji options...');
    
    // Test with different base URL
    const customTwemojiHtml = twemoji.parse('🎨', {
        base: 'https://cdn.jsdelivr.net/gh/twitter/twemoji@14.0.2/assets/',
        folder: 'svg',
        ext: '.svg'
    });
    console.log('Custom base Twemoji HTML:', customTwemojiHtml);
    
    // Test callback function
    const callbackResult = twemoji.parse('🌈', {
        callback: function(icon, options, variant) {
            console.log('Callback - Icon:', icon, 'Options:', options, 'Variant:', variant);
            return 'https://cdn.jsdelivr.net/gh/twitter/twemoji@14.0.2/assets/svg/' + icon + '.svg';
        }
    });
    console.log('Callback result:', callbackResult);
}

// Alternative method using SVG with manual conversion
async function testTwemojiSVG() {
    console.log('\nTesting SVG approach with manual conversion...');

    const canvas = createCanvas(400, 200);
    const ctx = canvas.getContext('2d');

    // Fill with light gray background
    ctx.fillStyle = '#f0f0f0';
    ctx.fillRect(0, 0, canvas.width, canvas.height);

    const testEmoji = '🎯';

    try {
        // Get SVG URL using Twemoji
        const twemojiHtml = twemoji.parse(testEmoji, {
            folder: 'svg',
            ext: '.svg'
        });

        const svgUrlMatch = twemojiHtml.match(/src="([^"]+)"/);
        if (svgUrlMatch) {
            const svgUrl = svgUrlMatch[1];
            console.log(`SVG URL: ${svgUrl}`);

            // Fetch SVG content as text
            const response = await fetch(svgUrl);
            if (response.ok) {
                const svgText = await response.text();
                console.log('SVG content length:', svgText.length);

                // For now, just draw a placeholder since SVG handling is complex
                ctx.fillStyle = 'blue';
                ctx.fillRect(50, 50, 72, 72);
                ctx.fillStyle = 'white';
                ctx.font = '16px Arial';
                ctx.textAlign = 'center';
                ctx.fillText('SVG', 86, 90);
                ctx.fillText('Placeholder', 86, 110);

                console.log('SVG approach: Created placeholder (SVG->Canvas needs additional processing)');
            }
        }
    } catch (error) {
        console.error('SVG test error:', error);
    }

    // Save SVG test result
    const buffer = canvas.toBuffer('image/png');
    const outputPath = './output/emotest-svg.png';

    if (!fs.existsSync('./output')) {
        fs.mkdirSync('./output');
    }

    fs.writeFileSync(outputPath, buffer);
    console.log(`SVG test image saved to: ${outputPath}`);
}

// Alternative method using twemoji.convert
async function testTwemojiConvert() {
    console.log('\nTesting twemoji.convert method...');

    const testText = 'Hello 😊 World 🌍!';

    // Convert text with emojis to HTML with image tags
    const convertedHtml = twemoji.convert.toCodePoint(testText);
    console.log('Converted text:', convertedHtml);

    // Test individual emoji conversion
    const emojiCodePoint = twemoji.convert.toCodePoint('🎯');
    console.log('Emoji code point for 🎯:', emojiCodePoint);

    // Test getting direct URLs
    console.log('\nTesting direct URL construction...');
    const directPngUrl = `https://cdn.jsdelivr.net/gh/jdecked/twemoji@16.0.1/assets/72x72/${emojiCodePoint}.png`;
    const directSvgUrl = `https://cdn.jsdelivr.net/gh/jdecked/twemoji@16.0.1/assets/svg/${emojiCodePoint}.svg`;

    console.log('Direct PNG URL:', directPngUrl);
    console.log('Direct SVG URL:', directSvgUrl);
}

// Run the tests
async function runTests() {
    try {
        await testTwemojiCanvas();
        await testTwemojiSVG();
        await testTwemojiConvert();
        console.log('\nAll tests completed!');
    } catch (error) {
        console.error('Test failed:', error);
    }
}

// Execute if run directly
if (require.main === module) {
    runTests();
}

module.exports = {
    testTwemojiCanvas,
    testTwemojiSVG,
    testTwemojiConvert,
    runTests
};
