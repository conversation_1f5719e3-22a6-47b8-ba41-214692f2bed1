const express = require("express");
const { createCanvas, loadImage, registerFont } = require("canvas");
const fs = require("fs");
const path = require("path");
const { parse } = require("@twemoji/api");
const fetch = require("node-fetch");
const app = express();
app.use(express.static(path.join(__dirname, "..", "public")));

// Function to check if file exists
function fileExists(filePath) {
  try {
    return fs.existsSync(filePath);
  } catch (err) {
    return false;
  }
}

// Register the fonts if they exist
const fontPaths = {
  regular: path.join(__dirname, "..", "public", "NotoSans-Regular.ttf"),
  emoji: path.join(__dirname, "..", "public", "NotoEmoji-Regular.ttf"),
};

if (fileExists(fontPaths.regular)) {
  registerFont(fontPaths.regular, { family: "NotoSans" });
} else {
  console.error(`Font file not found: ${fontPaths.regular}`);
}

if (fileExists(fontPaths.emoji)) {
  registerFont(fontPaths.emoji, { family: "NotoEmoji" });
} else {
  console.error(`Font file not found: ${fontPaths.emoji}`);
}

app.use((req, res, next) => {
  res.header("Access-Control-Allow-Origin", "*");
  next();
});

function error(state, message, res) {
  if (state === "throw") {
    res.json({ Error: message });
  }
}

function getInfo(req, res) {
  const { name, icon, money, text } = req.query;
  if (name && icon && money && text) {
    return { name, icon, money, text };
  } else {
    error("throw", "Bad Request: Parameters are missing.", res);
    return null;
  }
}

function color(money) {
  money = parseInt(money, 10);
  if (money <= 19999) {
    return [
      "#1565C0",
      "#1565C0",
      "rgba(255, 255, 255, 0.7)",
      "rgb(255, 255, 255)",
      "rgb(255, 255, 255)",
    ];
  } else if (money <= 49999) {
    return [
      "#00B8D4",
      "#00E5FF",
      "rgba(0, 0, 0, 0.7)",
      "rgb(0, 0, 0)",
      "rgb(0, 0, 0)",
    ];
  } else if (money <= 99999) {
    return [
      "#00BFA5",
      "#1DE9B6",
      "rgba(0, 0, 0, 0.7)",
      "rgb(0, 0, 0)",
      "rgb(0, 0, 0)",
    ];
  } else if (money <= 199999) {
    return [
      "#FFB300",
      "#FFCA28",
      "rgba(0, 0, 0, 0.7)",
      "rgb(0, 0, 0)",
      "rgb(0, 0, 0)",
    ];
  } else if (money <= 499999) {
    return [
      "#E65100",
      "#F57C00",
      "rgba(255, 255, 255, 0.7)",
      "rgb(255, 255, 255)",
      "rgb(255, 255, 255)",
    ];
  } else if (money <= 999999) {
    return [
      "#C2185B",
      "#E91E63",
      "rgba(255, 255, 255, 0.7)",
      "rgb(255, 255, 255)",
      "rgb(255, 255, 255)",
    ];
  } else {
    return [
      "#D00000",
      "#E62117",
      "rgba(255, 255, 255, 0.7)",
      "rgb(255, 255, 255)",
      "rgb(255, 255, 255)",
    ];
  }
}

async function wrapTextWithEmoji(ctx, text, x, y, maxWidth, lineHeight) {
  const words = text.split(" ");
  let line = "";
  let testLine = "";
  let testWidth = 0;
  for (let i = 0; i < words.length; i++) {
    const word = words[i];
    testLine = line + word + " ";
    testWidth = ctx.measureText(testLine).width;

    if (testWidth > maxWidth && i > 0) {
      await drawTextWithEmoji(ctx, line, x, y);
      line = word + " ";
      y += lineHeight;
    } else {
      line = testLine;
    }
  }
  await drawTextWithEmoji(ctx, line, x, y);
  return y + lineHeight;
}

// Helper function to extract the src attribute from the img tag
function extractEmojiSrc(html) {
  const regex = /<img[^>]*src="([^"]+)"[^>]*>/;
  const match = html.match(regex);
  return match ? match[1] : null;
}

// Twemoji Image Function
function sanitizeText(text) {
  // Hilangkan karakter VARIATION SELECTOR-16 (U+FE0F)
  text = text.replace(/\uFE0F/g, "");

  // Normalisasi teks untuk menghilangkan karakter combining marks
  text = text.normalize("NFKD").replace(/[\u0300-\u036F]/g, "");

  // Hilangkan karakter Unicode khusus yang mungkin menyebabkan masalah
  text = text.replace(/[\u200B-\u200D\uFEFF]/g, ""); // Zero-width dan karakter spesial

  return text;
}

// Twemoji Image Function
async function drawTextWithEmoji(ctx, text, x, y) {
  ctx.font = '12px "NotoSans", "NotoEmoji"';
  const sanitizedText = sanitizeText(text);
  const words = sanitizedText.split(/(\s+)/); // Split by space while keeping the spaces
  for (const word of words) {
    if (!word.trim()) {
      x += ctx.measureText(word).width; // Add space width
      continue;
    }

    for (const char of word) {
      const emojiHtml = parse(char, { folder: "svg", ext: ".svg" });
      const emojiUrl = extractEmojiSrc(emojiHtml);

      if (emojiUrl) {
        try {
          const response = await fetch(emojiUrl);
          const buffer = await response.buffer();
          const image = await loadImage(buffer);

          ctx.drawImage(image, x, y - 12, 16, 16); // Adjust Y position and size of emoji
          x += 20; // Spacing between emojis
        } catch (error) {
          console.error(`Failed to load emoji: ${error.message}`);
        }
      } else {
        ctx.fillText(char, x, y);
        x += ctx.measureText(char).width; // Add width of character to X position
      }
    }
  }
}

app.get("/generate-image", async (req, res) => {
  const info = getInfo(req, res);
  if (!info) return;

  const { name, icon, money, text } = info;
  const colors = color(money);
  const hexToRgb = (hex) => {
    const bigint = parseInt(hex.slice(1), 16);
    const r = (bigint >> 16) & 255;
    const g = (bigint >> 8) & 255;
    const b = bigint & 255;
    return [r, g, b];
  };
  const colorRgb = colors.map(hexToRgb);

  const iconUrl = `https://api.pocopota.com/icon-maker?size=40&url=${icon}`;
  const width = 335;
  const maxWidth = 303;
  const lineHeight = 21;

  const tempCanvas = createCanvas(width, 1000);
  const tempCtx = tempCanvas.getContext("2d");
  tempCtx.font = '12px "NotoSans", "NotoEmoji"';
  const textHeight = await wrapTextWithEmoji(
    tempCtx,
    text,
    0,
    0,
    maxWidth,
    lineHeight
  );
  const height = 56 + textHeight + 16;

  const canvas = createCanvas(width, height);
  const ctx = canvas.getContext("2d");
  ctx.textDrawingMode = "glyph";
  ctx.fillStyle = `rgb(${colorRgb[0][0]}, ${colorRgb[0][1]}, ${colorRgb[0][2]})`;
  ctx.fillRect(0, 0, width, 56);
  ctx.fillStyle = `rgb(${colorRgb[1][0]}, ${colorRgb[1][1]}, ${colorRgb[1][2]})`;
  ctx.fillRect(0, 56, width, height);

  ctx.font = 'bold 14px "NotoSans", "NotoEmoji"';
  ctx.fillStyle = colors[2];
  await wrapTextWithEmoji(ctx, name, 72, 19, maxWidth, lineHeight);

  ctx.font = 'bold 16px "NotoSans", "NotoEmoji"';
  ctx.fillStyle = colors[4];
  ctx.fillText(`Rp${Number(money).toLocaleString("id-ID")}`, 72, 38);

  ctx.font = '12px "NotoSans", "NotoEmoji"';
  ctx.fillStyle = colors[4];
  await wrapTextWithEmoji(ctx, text, 16, 77, maxWidth, lineHeight);

  loadImage(iconUrl)
    .then((iconImage) => {
      ctx.drawImage(iconImage, 16, 8, 40, 40);
      res.setHeader("Content-Type", "Content-Type", "image/png");
      res.send(canvas.toBuffer());
    })
    .catch((err) => {
      error("throw", "Failed to load icon image.", res);
    });
});

module.exports = app;
